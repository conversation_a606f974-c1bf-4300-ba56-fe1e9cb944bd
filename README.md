# Speech-to-Text Project

This project uses OpenAI Whisper for speech-to-text transcription.

## Setup

### Virtual Environment

The virtual environment has been successfully initialized and configured for Python 3.13.

#### Quick Start
1. **Activate the virtual environment:**
   - PowerShell: `.\activate_venv.ps1`
   - Command Prompt: `.\activate_venv.bat`
   - Manual: `venv\Scripts\activate`

2. **Run the main script:**
   ```bash
   python src/main.py
   ```

### Dependencies

All core dependencies have been installed and are working:
- ✅ OpenAI Whisper
- ✅ PyTorch 
- ✅ TorchAudio
- ✅ PyYAML
- ✅ Loguru
- ✅ Rich
- ✅ Other supporting packages

### Known Issues

#### pydub Compatibility (Python 3.13)
- **Issue**: pydub has compatibility issues with Python 3.13 due to the missing `audioop` module
- **Impact**: Audio format conversion functionality in the main script may not work
- **Workaround**: 
  - Use WAV files directly (Whisper supports WAV natively)
  - Use ffmpeg for audio conversion outside of Python
  - Consider using Python 3.12 if pydub functionality is essential

#### Whisper Model Loading
- First run will download the selected model (e.g., ~72MB for 'tiny' model)
- Models are cached locally for subsequent runs

## Usage

The main script supports both single-file and batch processing modes. See the existing configuration files in the `src/` directory for examples.

## Files Structure

- `src/main.py` - Main transcription script
- `src/batch_transcription_*.yaml` - Batch processing configuration examples
- `output/` - Directory for transcription results
- `venv/` - Python virtual environment
- `requirements.txt` - Python dependencies
