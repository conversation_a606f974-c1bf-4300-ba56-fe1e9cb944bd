 Hello community! Forget the old large language model, today we have something new. A large concept model. Yeah, it all started with <PERSON>a. Meta had a problem, you know? And they had more than 200 languages here on its social media platforms. And the question was, how can we reduce the cost for the human operator and the human translator and this human for scaling your growth into new markets? How to increase the profits? Now, simple way was to abstract the language away from the communication. So we have the concept of the message, the content of the message, and the language in which the message is encoded. English, French, Spanish, Italian, Portuguese, whatever you like. So the solution is abstract the human language away and then you just work here with a mathematical concept of the content of a human specific message. Now the old way was large language model predicted the next token. A token could be a word. Token could be just some characters here, with a specific probability, some autoregressive methodology. Now the new idea is, we work here with a concept of a content of a message. Now this is some beautiful idea, because it simplifies so much for Meta. Meta says humans operate at multiple levels of abstraction, well beyond a single word or maybe a single character, to analyze information and to generate a creative concept. content that you can publish on social media, on their social media platforms. So this LCM is now a new architecture which operates on an explicit higher level semantic representation which is in itself beautiful. And you know we encountered this already a week ago when I showed you here in this video of mine. We can use here in context learning not just with few short examples but we move from the simple examples given in our our prompt to higher order cognitive reasoning complexes that we provide now in the prompt as a few shot examples quotation mark to our LLM and we jump here a whole complexity level so that the LLM has not to find the hidden pattern and the examples but we already give away here the reasoning for finding a solution now Meta is doing the same more or less here. Meta tells us we define a concept as an abstract atomic idea. In practice, a concept would often correspond to a single human sentence in a text document. And then Meta tells us we would aim to train a new embedding space, a very specific embedding space optimized for a reasoning architecture. So they are both heading more or less in the same direction. They are trying to find here a common ground. Meta tells us that the idea as we choose here an existing and freely available sentence embedding mechanism called Sonar. Now if you don't remember here 2023, beautiful publication here by Mithya in India, Sonar sentence level multimodal and language agnostic representation. And already at that time here we had that Mithya was working here, how can we handle 200 languages? And they are researching this now for years and years and they are Sonar Our technology relies on a transformer, a complete transformer and an encoder-decoder, not just an autoregressive architecture, initialized here with a pre-trained machine translation model weight structure. The architecture meter used here to train Sona on parallel text data goes through a single-vector bottleneck that represents the full sentence and does not use token-level cross-attention like the other models do. So the main idea already years ago was hash- this and then we only have a representation of the complete concept of the content in a single vector in a real particular vector space if you want to have here the github if you want to explore this yourself if the code there some beautiful updates just days ago but careful because here it says here for the license is unknown and some MIT license is found so I didn't know if you can use this for your behavior so just want to make you aware of this limitation. So, Meta tells us now each sentence is encoded now in this new methodology with Sonar to achieve here a sentence embedding. And we know sentence embedding, this is nothing special now. But now a sentence embedding, given that this is the atomic item of reasoning, we can now build in this vector space sequences of concepts, sequences of sentences. And the LCM can now be trained. we train our llms this means it can acquire knowledge but now think about it if we have 10 languages and in each language we have a little bit of knowledge in english we have a little bit in french we add a little bit and in german we add a little bit this system can now learn from all the different languages and all the different modalities maybe you have spoken or you have written text and it will be aggregated here to an abstraction so if you have a platform i don't know with 100 languages all the ideas can be now integrated in a mathematical space given here whatever language comes in the knowledge will be encoded meta tells us you know there's another beautiful point here because if we handle here long context or long form outputs and i've shown you that here berkeley came up here with ring attention and google uses this for 2 million token context length and now meta tells us here you know this transformer has here this problem with quadratically increases here the complexity with the sequential length that we have with our token and meta tells us our lcm operates here on sequences which are at least an order of magnitude shorter than the normal language that we have when we use transformer so i thought hey wait a second so it's not using here and optimization because it is just reducing here the length of their encoding sequence their sentences and yeah that's what it turned out because look here this sentence of mine a normal sentence that I would say is as you stood beneath the sprawling canopy of stars her mind adrift in the unfathomable elegance of celestial mechanics the swirl of the hands of Galaxy calculating here the dark matter dense and the silent pull of unseen forces here, recalculating here the dark energy, a gentle touch squashed her shoulders, drawing her from the depths of her thoughts and turning her towards the quiet hope of seeing her friend's familiar face, her heart poised between the vastness of the cosmos and the intimacy of a human connection. So you see, a simple sentence I use every day. And now, with Mita's limitation, Mita tells us a sentence with 10 tokens. Max 20 tokens for this. system to work. And I ask here, so okay, then transfer this. And the sentence is, she gazed at the stars, dreaming of the mysteries they held. So you see the semantic richness, the semantic content now of one sentence might pose here kind of a little tiny problem, but hey, we are for Meta operating here for social media platforms communication. And they are real short and I understand it for Meta, 10 tokens. That's it. That's if you are on Facebook, on WhatsApp, on wherever you are. A sentence with 10 tokens. This is, I would say, the normal sequence length. So it works. Great. But how a meter acknowledges that dealing with large text corpora presents several practical limitations. So at first they have to find here some robust automatic text segmentation techniques if they encounter sentences that are longer than 10 or 20 tokens. And second, if some content is really long and complex, this might really negatively impact the quality of this encoded embeddings here, going here with the sumo architecture. And this is particularly true here for text if you have a scientific domain. Because if you want to communicate something in the scientific domain, you have rather length technical abstracts with long sentences, with deep semantic content going into the explanation. of each and every fact of the experiment so clearly we see here the limitation of this system and then they have a gorgeous idea and i was fascinated by this because they say you know what we do now we combine now two complete two architectures we have the diffusion transformer and we have here our llms the classical transformer and we will combine both methodologies and form here a diffusion based large context model model. If you read the original paper, really nice, but here for this video I would like to give you a simplification. So if you are not at all familiar with the Latent Diffusion Model and Stable Diffusion Model and the original Autoencoder and Unit and CLIP, this is the video for you. Very short summary, we have at the beginning a sentence representation now in this new mathematical embedded space, what we call the Sona space. And we calculate simple embed laddings of our very short sentences. And let's say I have a 20-dimensional vector representation now of a particular sentence. And now, you remember I told you here when we were talking about sentence transformer and S-Bird and Bird systems, you can build subspaces. Because maybe you say the subject semantics go from dimension 1 to 3, if it's a real simple sentence, then the verb and the action you can encode here in the dimension 4 to 6 of the 20-dimensional vectors, then... from seven to nine you have objects, then you have grammar, then you have context dependencies. So you can build this. And imagine we have a 1000 dimensional mathematical space or 2000 or 10,000 dimensional space. So we can encode concepts. However, there is a logical mathematical limitation, how much different data you can encode. So therefore, how we build those sonar embedding space is important and it has to be done in a mathematical optimized way. But then if we have it, we do have now the diffusion process and please this is important we have here the refinement of the embeddings it is not anymore a semantic construct but we have a mathematical vector it is the classical diffusion process you have the forward process where you add a specific type of noise then you have to reverse process where you do that denoising and then you have the output of the diffusion then you have a clean embedding represents now the review newly generated sentence. Classical way, there's nothing particular to this, but notice that here, if we bring in the transformer, the transformer, and I thought maybe Meta wants to get rid here of the transformer architecture because it is patented by Google, but no, they integrated the transformer quite massively in the architecture, and I think the transformer is really central also to this LCM because it supports both the context encoding process and the diffusion process itself. And you have this simple, if you look here at Sonar, the transformer encodes here the prior context into a conditioning vector, and this conditioning vector guides here the denoising process. And the noise prediction process you have also that at each diffusion step at a particular time t, the transformer predicts the added noise, the epsilon theta, from the noisy embedding, enabling here the denoising process. And if you have multi-sequence task and multi-sentence task here, you have here the trans... in the hierarchical processing of the sentence embeddings, if they are in a hierarchical structure. Capturing here the inter-sentence relationship and building here coherent content across the text. And if the text is only there, if you want the atomic content, this is not an easy task. And remember, Transformer can have some beautiful hallucination all the way, and you have this now interwoven with the diffusion model. So, I think there are challenges ahead for this methodology. Thank you. Then we have a simple output, beautiful, the clean embedding represents now a specific sentence here, and we have our downstream reasoning task. If you want to see here the key role of the diffusion and of the transformer architecture in here, this new LCM, this is here, very short summary for you. Now they experimented here with a further optimization step, either go with one tower or two towers. The real simple. without forgetting the mathematics here, a one tower architecture is more simple, is more efficient, and you have here a lighter context or lower complexity that you want to handle. If you have here more complex content, like in a scientific text, they would recommend to go with a two tower architecture. So you separate here the context encoding and the noise prediction from each other. You build an own tower architecture. architecture for context encoding and a separate one for the noise prediction. And this helps here to improve the diffusion based reasoning in the LCM framework. Again here, what is a one tower? A single transformer for both tasks or two transformers. One for the context encoding and one transformer for the denoising, separating, more specialized, more expert system in their own right. The task is here a combined task, may cause some inference problems. The tasks are here entangled. if it is possible allowing here for the specialization to take place the performance here as i told you for a simpler complex task for less robust a complex task if you go into science or mathematics or logical reasoning i would recommend here the two tower structure yeah simpler lower lower memory cheaper higher computational cost and higher memory cost if you go with the two tower architecture but let's look at this diffusion process a little bit more in detail and i think they did a really beautiful description here of the idea and i really recommend here you read the original documentation by meter so short summary if you want why this diffusion process and why they focus on this and why they think that this is the way forward to the transform architecture this diffusion process if we start with the basic is responsible here for gradually refining here a noisy representation of the sentence embedding into a clean meaningful representation. So imagine you start with a rough idea of what a sentence might mean, represented here by a vector. The vector is full of noise. We have multiple different types of noise distribution. We'll have a look at this later. And our task is to make this noisy vector progressively cleaner and clearer and really have a convergence to one specific vector. So at each step of this refinement, the diffusion process asks the transformer now to help figure out what part of the noise should be removed to make the embedding more aligned with the intended meaning or the task that were specified. In this interaction, and this is now also another critical piece of the architecture, this interaction happens iteratively. Why? Because if you remember U-Net and the diffusion process and everything, this provides here the current noise embedding and the transformer predicts how to clean it step by step. Thank you. The diffusion process then applies this correction and passes the updated embedding back to the transformer for the next step. This back and forth continues until the embedding is fully refined and represents here a clear sentence embedding. Once the clean embedding is ready, it can be decoded back into a human text and used here in our reasoning task. This diffusion process handles the gradual refinement of sentence embedding while the transformer supplies you the knowledge and the context needed to clean up the noise at each step. step. Now you understand, we have now, if we have here the transformer, all the problems we have with transformers alone, we bring in into this LCM architecture. So wherever we had a problem with LLMs, we will have a problem with LCMs, because now the transformer are an integral part here. How much they influence here this elegant solution of the diffusion process to clean up and noisy vector representation. This is now we have to experiment this. This was published yesterday and I had no time to run some real operative text. Now, I ask myself why this noise? What does it bring to the table? Now, you remember in our real world scenarios, a model, LCM, may encounter incomplete sentences. Sentences that sound strange or noisy inputs or whatever. And now the idea of meter is to that this diffusion training process equips our LCM with the ability to make sense of such imperfect data and produce some coherent, logically structured, beautiful outputs. This framework introduces a probabilistic element, allowing the model to explore multiple potential meanings or refinement of a specific vector representation that might be noisy and therefore adding here robustness and flexibility, but you also kind of reduce it to a classification problem. So careful! This is a delicate balance that we have to handle here. The noise is added on purpose during the training to help the model learn to refine embeddings effectively and handle this kind of uncertainty it will encounter in real-world examples in the sentence representation than during the inference run. Great! So the act of denoising doesn't just fix the noise that we know from images. but it makes the system understand in a certain interval, in a certain limit, how to clean up vector representation embedded vectors. Particularly important is the robustness of this methodology for the reasoning process. But you know, those vectors are crucial for the whole process. Because the vector is the core representation that the model uses for reasoning, for comparison, and for generation of new clean vector representation. So the denoising at this level ensures that the model can handle noisy or ambitious inputs while preserving the essential meaning. Beautiful, and it can be decoded back into the human readable sentences. This is great. And we operate here with our denoising algorithm only in the mathematical space of the vector embeddings, ensuring that the model learns to handle this uncertainty. Now let's look at little bit deeper in detail. The large concept model, LCM, here. You have the transformation of a sentence that is now the perfect representative of the concept of a content in a sentence encoded, is now further encoded here in a mathematical space that we built based on the training data. It is configured based on the training data of the LCM, so it builds up a vector space space and a vector subspace with the corresponding Sona embedding space to handle pre-training transformer-based encoders such as those that we have here in BERT and Robertan and the sentence level encoders. Those are my S-BERT models, my sentence BERT models. Now, remember the Sona embedded space mentioned here in the paper I just showed you five minutes ago is specifically pre-trained here to work on multiple languages. So it uses here to transformer-based architecture that is fine-tuned for multilingual inputs. But however, if you look at specific at the data set they use for the pre-training, those are rather short sentences. So this works perfect for Meta, for their social media platforms. Short sentences in the training data sets, short sentences in the real world example on the social media platforms. But the moment you go out of the social media platforms and you have, let's say, a scientific sentence or my beautiful sentence, the system might encounter deep problems. Great summary. Oh, just wait a second. So here we go now. The LCM methodology, a real nice gorgeous idea. Transformer-based encoder maps sentences, remember, that represent now only the concept of the content, to new mathematical vectors in a vector space to be built given the pre-training content. And we call this the Sonar Embedding Space. Of course, we have tokenization, contextual encoding, sort of transformer aggregation to produce a single embedding vector for each sentence. Again, the sentence is the concept of the content and is now only a high dimensional single vector. And then the hope is in this high dimensional vector space, maybe we can do some vector operation with this to come to multi-hop reasoning. I don't see it yet, but to tell you, it's just the first day I have a look at this. So, transformer is crucial for ensuring here that the embedding captures here the rich nuanced meaning of the sentence, making it suitable for tasks like reasoning and multimodal integration and generalization across languages. If this would work, and not just on short sentences, but in general, we would have all the sentences of the world abstracted away to their core of their meaning and then this core of the meaning of a sentence can be translated in i don't know 1000 other sentences huge problem with syntax with grammar with whatever there is but i understand that the idea is what is the core idea of a sentence and if i can map this into a mathematical embedded space i can then operate with my matrix matrix multiplication with my systems on that space. Okay, short summary. Yeah, if you want to learn more about sentence transformer, BERT system, SBERT system, I have here a playlist with 40 videos. I show you everything there is to know about it. So, I have a deep dive. Currently, I have 60 videos on SBERT, but in this playlist, I only have the first 40 videos. Now, what are the limitations? Let's be clear. This is... first idea that is nice, beautiful, but we are not there yet. So, sentences in this sonar space, despite being represented as a continuous vector, remain discrete combinatorial objects, no? Now, there is a problem, because you remember the original diffusion process, we were here not in a discrete space. So, this makes the diffusion modeling struggle here on this particular text modality. The choice and design of this embedding space, built with our expert models, plays here the crucial role in the LCI modeling approach. If the space is not optimally built, the system will fail, whatever you apply to it. This SONA model was trained here on very specific training data, this was bi-text machine translation data, containing rather short sentences, and not at all reasoning, not at all at all complex topics not at all whatever machine translation data only so to take this mathematical space as the base space i think we have to do better in the next step of the evolution of this and we have to have a broader scope of training data mathematical data physical data normal data normal length data and so on yeah they first experimented here with 1.6 or 1.7 7 billion free trainable parameter models and then they tried to scale it to a 7b model. But yeah, there are a lot of problems associated with this. As always, data. Data quality is so important. So any frozen encoder now, which is learned in a different data context, if it's not really only the machine translation data that the Sonar model was trained with, if there's any different data context, maybe any different language that was not there. you run into troubles. And with no a priori strong connection here to the LCM modeling, this encoder might be suboptimal to say the least. So data relevance, data quality, data coherence, data complexity, all the things that we have here in the transformer, you will find here immediately and maybe even more so because now you have to even find a coherence here with the... discrete combinatorial space in the diffusion process. Okay, yeah, here we have now on Facebook research here, large concept model, remember this is not what you find in the original documentation by Meta. Here you have it, everything updated yesterday, 11 hours ago, here you find the code. Interesting enough, now we have an MIT license, so we are free and allowed to play with this. But yeah, this will be the task. for my weekend for example if you are interested this is the repo to have a look at and yeah this is the study by the way so december 12 2024 meta beautiful new idea real complex to read it i think it's close to 50 pages quite some mathematical heavy stuff but i hope with my video that i can give you here the general idea i give you here the i opened doors for you to understand exactly what is going on here, where the limitations, what is great, how the models work, and if you want to have more details in the implementation, please have a look at this paper or at the GitHub implementation. Just to the last example I want to show you with what complexities we are dealing here is here from the original paper by Meta. And this is the system prompt. This is the original system prompt for the generation here of a topic description that is real. short you remember 10 tokens so they say your topic description generator yes yes yes and then we have here one example so of the in context learning a one-shot example and we say hey one day one neighborhood of the city was completely devastated glass windows were shattered shops turned upside down and many civilians superman instantly recognized the signature of one of his old enemies and he had bodily beaten here in the past and it was a message to him i will challenge you come and find me you see long so now the examples we provide here as the best example here of the system prompt is okay and our example of a good topic description an old enemy of superman voltar appeared and challenged him you see in this funny sentences this might work but this extraction of the content and then to further meter this to the concept of the content is here an easy task. But think about theoretical physics or think about biochemistry, think about financial transactions, think about dependencies in those topics, then these very short sentences might break. But however, as we talked at the very beginning, Meta has a specific problem and Meta developed a specific tool. And you always develop a specific tool to your specific problem. And for the short very short communication patterns that you have on social media platform this might work but if you want to apply this now to your normal let's say large language model apps be aware of the limitation although the idea is absolutely fascinating and i'm really looking forward to you to develop this further read more about this approach here in the future and if you're interested hey why not subscribe and i could see you in my next video