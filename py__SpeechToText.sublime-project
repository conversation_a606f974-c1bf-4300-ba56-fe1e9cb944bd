{"folders": [{"path": ".", "folder_exclude_patterns": ["__pycache__", "logs", "env", "venv", ".git", ".svn", ".hg", ".idea", ".vscode", "node_modules", "*.egg-info", "dist", "build", ".DS_Store"], "file_exclude_patterns": ["*.pyc", "*.pyo", "*.log", "*.tmp", "*.sublime-workspace", ".DS_Store", "*.swp"]}], "settings": {"tab_size": 4, "default_line_ending": "unix", "translate_tabs_to_spaces": true, "ensure_newline_at_eof_on_save": true, "trim_trailing_white_space_on_save": true, "python_interpreter": "$project_path/venv/Scripts/python", "python_formatter": "black", "python_linter": "flake8", "python_format_on_save": false}, "build_systems": [{"name": "py__SpeechToText.sublime-project", "cmd": ["$project_path/venv/Scripts/python", "-u", "${file}"], "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)", "selector": "source.python", "shell": true, "working_dir": "${project_path}"}]}