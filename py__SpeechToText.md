# Project Files Documentation for `py__SpeechToText`

### File Structure

```
└── src
    ├── batch_transcription_en.yaml
    ├── batch_transcription_nor.yaml
    └── main.py
```
### 1. `src\batch_transcription_en.yaml`

#### `src\batch_transcription_en.yaml`

```yaml

# ---------------------
# Optional Settings
# ---------------------
logging_level: "DEBUG"

# ---------------------
# Global Configuration
# ---------------------
output_dir: "transcriptions"  # Directory where all transcriptions will be saved.
language: "english"         # Default language for transcription. Override per file if needed.
model: "medium"               # Default Whisper model to use. Options: tiny, base, small, medium, large, etc.
temperature: 0.0              # Default temperature setting for transcription.

# ---------------------
# Transcription Tasks
# ---------------------
files:
  - file_path: "/absolute/path/to/audio1.mp3"
    model: "medium"                     # [list of models]
    temperature: 0.0                    # ...

  - file_path: "/absolute/path/to/audio1.mp3"
    model: "large"                      # [list of models]
    temperature: 0.0                    # ...


```
### 2. `src\batch_transcription_nor.yaml`

#### `src\batch_transcription_nor.yaml`

```yaml
# ---------------------
# Optional Settings
# ---------------------
logging_level: "DEBUG"
# ---------------------
# Global Configuration
# ---------------------
output_dir: "transcriptions"  # Directory where all transcriptions will be saved.
language: "norwegian"         # Default language for transcription. Override per file if needed.
model: "medium"               # Default Whisper model to use. Options: tiny, base, small, medium, large, etc.
temperature: 0.0              # Default temperature setting for transcription.
# ---------------------
# Transcription Tasks
# ---------------------
files:
  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Adresser konfliktene du har i ditt eget liv først.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Alle er tjent ved at du ikke er så bastant.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Alle har evnen til å stille seg selv spørsmålet Hvorfor.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Alle har kjærlighet å gi.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Alle har opplevd konsekvensene av å misforstå hverandre.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Alle kan relatere til å måtte ha meninger.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Alle trenger å vite at de tar feil.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Avlaste Byrde 1 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Avlaste Byrde 1 - 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Begge sider har sett hverandres hykleri.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Budskap 1 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Cancel-culture er et resultat av at folk ikke klarer å kontrollere egne følelser.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - De aller fleste tror de gjør det rette.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - De fleste av oss har bedre intensjoner, enn hva andre oppfatter om oss.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - De fleste konflikter er pga misforståelser.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - De nyanserte må lære på kommunisere med de polariserte, det er vårt eneste håp.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - De spørsmålene har kommet som en konsekvens av at jeg har tatt feil.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Demokrati er en feiring av å kunne være uenige.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Den minste forutsetning for å kunne bidra med noe positivt.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det du leser på nettet har ikke den samme effekten på deg.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det er en enorm utfordring å oppnå det jeg ønsker.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det er en forutsetning at du føler deg forstått før du kan endre mening.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det er frustrerende å ikke føle seg forstått.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det er ikke sikkert alle ønsker å forstå seg selv.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det er ingenting av det jeg sier som er unikt, men det er kombinasjonen som utgjør et unikt perspektiv.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det er mange som uvitne går imot sin egen ideologi.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det er mange som vet ting, uten å vite hvorfor de vet det.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det er viktig å merke seg at nåværende strategi ikke fungerer.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det finnes mange gode argumenter, men det finnes alltid bedre spørsmål.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det som er moralsk korrekt nå, trenger ikke å være det senere.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det som er skummelt med å eksponere sin egen fasade.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det som former deg mest som menneske, er det du gjør for å cope med lidelse.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det viktigste budskapet.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Det å realisere at du tar feil om noe, betyr bare at du har noe å jobbe med.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Din mening er tilfeldig, om du ikke vet hvorfor du har den.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Diskusjon 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Diskusjon 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Du er tjent ved å prøve å like ting.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Eksempel på ydmykhet.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - En annen approach - presentasjon med musikk 1-1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - En annen approach - presentasjon med musikk 1-2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - En krig har aldri startet ved usikkerhet, usikkerhet er bedre enn skråsikkerhet.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - En objektiv betraktning 1 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - En objektiv betraktning 1 - 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Essensen av JRE.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Et av målene mine er å hjelpe på en annen måte.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Et eksempel på hvordan vi naturlig generaliserer.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Et eksempel på spørsmål jeg har stilt meg selv 1-1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Et eksempel på spørsmål jeg har stilt meg selv 2-1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Et overblikk over situasjonen.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Et perpektiv fra en som vanligvis lytter.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Evaluer deg selv før du dømmer andre.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Feil jeg har gjort.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Folk er veldig opptatt av å bli OPPFATTET som gode.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - For at et svar skal ha verdi for deg, så må du være investert i spørsmålet.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Forskjellen på venstre og høyre-siden ift. hvem vi lytter til.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Forsøk på objektivt perspektiv.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Først hva og hvorfor, deretter effektene av det 1-1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Først hva og hvorfor, deretter effektene av det 1-2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Gode intensjoner er ikke det samme som å være et godt menneske.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Gode svar har ingen verdi dersom spørsmålet ikke har blitt stilt 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Gode svar har ingen verdi dersom spørsmålet ikke har blitt stilt 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Gode svar har ingen verdi dersom spørsmålet ikke har blitt stilt 3.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Goofy animasjon av meg selv.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Grunnen til at jeg skal lage denne videoen.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Grunnen til at vi har tomme meninger.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hva er det jeg -ikke- trenger å si 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hva er det jeg -ikke- trenger å si 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hva er det som er BRA med mennesker i dag.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hva er grunnen til at jeg lager denne videoen.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hva som er motivasjonen min til å lage perspektiv-videoen.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hva som skal til for å forstå andre.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hvis jeg kan forstås, så har vi oppskriften på å få de kloke til å formidle sine.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hvis man har evnen til å endre mening, så har man mer tillit til seg selv.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hvordan jeg kan avslutte videoen.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hvorfor håp er så viktig for meg 1-1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hvorfor håp er så viktig for meg 1-2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hvorfor håp er så viktig for meg 1-3.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hvorfor håp er så viktig for meg 1-4.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hvorfor håp er så viktig for meg 1-5.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hvorfor skal jeg kunne gjøre verden bedre, når så mange har prøvd før.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hvorfor vi trenger å kunne påvirke hverandre.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hvorfor.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Hyklere kan identifiseres ved hvordan de tar vare på seg selv.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Ikke generaliser de som generaliserer.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Illustrere polarisering.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Innsikt i andres perspektiver.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 1 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 1 - 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 1 - 3.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 1 - 4.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 10 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 10 - 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 10 - 3.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 10 - 4.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 11 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 12 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 13 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 14 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 15 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 15 - 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 16 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 16 - 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 17 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 17 - 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 17 - 3.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 17 - 4.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 17 - 5.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 17 - 6.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 17 - 7.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 19 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 2 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 2 - 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 2 - 3.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 20 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 21 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 22 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 23 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 24 - 1 (SJEKK DENNE).mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 25 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 26 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 27 - 1 (SJEKK DENNE).mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 27 - 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 27 - 3.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 27 - 4.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 27 - 5.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 27 - 6.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 27 - 7.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 27 - 8.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 28 - 1 (SJEKK DENNE).mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 29 - 1 (SJEKK DENNE - AUTENTISK).mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 29 - 2 (SJEKK DENNE - AUTENTISK).mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 3 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 30.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 31 - Man blir straffet uten å ha blitt forstått.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 32 - Andres realitet.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 33 - Hvorfor ha håp.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 34 - Kanskje jeg må ha to videoer 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 34 - Kanskje jeg må ha to videoer 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 35 - VI HAR NOE TIL FELLES 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 35 - VI HAR NOE TIL FELLES 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 36 - Starte med alt jeg ikke vet.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 37 - Jeg må introdusere meg selv.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 37.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 4 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 4 - 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 5 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 6 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 7 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 8 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Introforslag 9 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg bryr meg om hva andre mener om meg, så jeg er kanskje en idiot.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg burde lage et nodetre.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg er avhengig av å ha håp til andre mennesker 1 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg er avhengig av å ha håp til andre mennesker 1 - 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg er villig til å eksponere min egen fasade.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg introduserer kun et perspektiv 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg introduserer kun et perspektiv 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg kan ikke få folk til å forstå alle, men jeg kan få de til å forstå meg.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg kan ikke ha for mye kjedelig intern dialog.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg kan ikke snakke om ting som ikke har noen verdi.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg kan spille mer på animasjon.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg kan utfordres på det jeg sier, dette er bare et perspektiv.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg må beskrive hvorfor jeg har det perspektivet jeg har.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg må gi folk en grunn til å ønske å lytte til meg.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg må gjøre meg selv sårbar, dette er en bevist formel for suksess.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg må identifisere det overordnede budskapet.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg må ikke tvile på at andre kan forstå meg 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg må ikke tvile på at andre kan forstå meg 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg må lære meg å forklare ting enklere.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg må være sårbar og relaterbar.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg skal formidle et perspektiv, ikke en mening 1-1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg skal formidle et perspektiv, ikke en mening 1-2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg skal formidle et perspektiv, ikke en mening 1-3.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg skal formidle et perspektiv, ikke en mening 1-4.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg skal formidle et perspektiv, ikke en mening 1-5.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg skal formidle et perspektiv, ikke en mening 1-6.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg skal ikke belære noen, jeg skal bare åpne meg.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg trengte å jobbe med meg selv.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg vil lage en video som er verdt tiden din.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jeg ønsker at vi skal ha et syn til felles.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jo mer du tror du vet, jo mindre villig er du til å åpne deg for at du tar feil.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Jo mer du vet, jo mindre forstår du.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Kanskje jeg må avsløre det egoistiske målet.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Kanskje jeg må høre på lydopptakene baklengs.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Kanskje jeg må være mer autoritær 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Kanskje jeg må være mer autoritær 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Kravet folk stiller til seg selv må reflektere hva du ytrer.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Lære folk hvordan de kan ha det bra.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Man burde søke kunnskap fra andre, men det er ikke lett.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Man bør være forsiktig med å ta ned statuer og symboler.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Man gjør ikke verden til et bedre sted ved å få de til å føle seg crap.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Man kan komme til samme mening med forskjellige resonementer.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Man kan være FOR rasjonell også.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Man trenger å forstå årsaken til problemene vi har.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Man vil alltids kunne utfordre folk som tror de har god moral.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Meninger.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Mennesker kan påføre seg selv større smerte enn dyr.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Min hypotese baserer seg på følgende.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Misforståelser er nesten alltid årsaken til konflikter.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Mitt løsningsforslag 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Mitt løsningsforslag 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Mitt løsningsforslag 3.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Mitt løsningsforslag 4.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Mitt mål, forbedre kommunikasjon.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Mitt utgangspunkt.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Motpoler 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Motpoler 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Motpoler 3.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Motpoler 4.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Motpoler 5.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Motpoler 6.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Må få folk til å bruke hodet.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Må ha ydmykhet i stemmen.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Måten vi diskuterer på, kan sammenliknes med et språk.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Nyhetsbildet bidrar til polarisering.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Nytt forslag perspektiv.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Når du ser at det ikke fungerer, så må du endre taktikk.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Perspektiv.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Politikk er uendelig komplisert, vi trenger derfor et overblikk.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Presentere det som en historie 1 - 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Presentere det som en historie 1 - 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Problemet med politisk korrekthet.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Realiseringene jeg formidler må være relaterbare.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Referanse jeg burde sjekke.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Refleksjoner etter diskusjon med pappa.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Rekkefølge på spørsmålet hvorfor jeg har meninger om ting.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Strategisk fremgangsmåte.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Svar på spørsmålet hvorfor jeg har meninger om ting.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Ta vare på deg selv for andre.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Tanker om en overordnet strategi.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - TestSnakkFortell.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - The true way to win a war, is to make the enemy your friend.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Utfordringene som kun erfares av èn generasjon.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Utviklingen av et samfunn må skje gradvis.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Verdien av perspektiv, er at det kan løse alle problemer.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Vi burde ha et ønske om å utfordre oss selv.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Vi er for ideologisk konsekvente.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Vi har litt for mye tillit til oss selv.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Vi har mer til felles enn det vi ikke har.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Vi har noe til felles, et ønske om å ha det bra.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Vi kan alle forbedre oss.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Vi mener sterkere enn det vi bør gjøre.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Vi må alltid være åpne for at vi kan ha misforstått hverandre.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Vi må lære av hverandre.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Vi må sammen prøve å finne de rette spørsmålene å stille lederne våre.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Vi trenger færre fiender.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Visualisere at de som tror de har like meninger, ikke har det.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Vær endring du ønsker å se 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Vær endring du ønsker å se 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Å lede andre til min egen realisering 1.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Å lede andre til min egen realisering 2.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Å lede andre til min egen realisering 3.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Økende polarisering kan tvinge deg til å fordømme andre.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Ønsker å avlaste andre fra en byrde.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Ønsker å endre retningen vi er på vei i.mp4"

  - file_path: "C:/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj__Perspektiv/_refs/lydopptak/Perspektiv - Ønsker å verifisere mine perspektiver.mp4"

```
### 3. `src\main.py`

#### `src\main.py`

```python
#!/usr/bin/env python3

import sys

# ---------------------------------------------------------------------
# Force stdin/stdout/stderr to UTF-8, especially on Windows,
# to handle Norwegian characters (æ, ø, å) properly.
# ---------------------------------------------------------------------
try:
    sys.stdin.reconfigure(encoding='utf-8')
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')
except AttributeError:
    pass

import argparse
import json
import logging
import os
import platform
import shlex
import socket
import yaml
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from enum import Enum
from pathlib import Path

import numpy as np
import whisper
from loguru import logger
from pydub import AudioSegment
from rich import box
from rich.console import Console
from rich.logging import RichHandler
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich.theme import Theme
from tqdm import tqdm


# LOGGER CONFIGURATION
# =======================================================

class LoggingLevel(Enum):
    """
    Enum for log levels.
    """
    TRACE = "TRACE"
    DEBUG = "DEBUG"
    INFO = "INFO"
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    WARNING = "WARNING"
    CRITICAL = "CRITICAL"


class LoggerConfiguration:
    """
    Configuration class for setting up and customizing the logger.
    """
    DEFAULT_VERBOSITY = "normal"
    DEFAULT_TIME_FORMAT = "[%X]"
    DEFAULT_THEME = {
        "logging.level.trace": "dim #b4009e",
        "logging.level.debug": "#bf00ff",
        "logging.level.info": "#3b78ff",
        "logging.level.success": "#12a50a",
        "logging.level.error": "#9b1616",
        "logging.level.warning": "#c0c005",
        "logging.level.critical": "black on bright_red",
        "log.time": "dim white",
        "traceback.border": "#5f0810",
    }

    VERBOSITY_LEVELS = {
        "quiet": LoggingLevel.WARNING.value,
        "normal": LoggingLevel.INFO.value,
        "verbose": LoggingLevel.DEBUG.value,
    }

    def __init__(
        self,
        verbosity: str = DEFAULT_VERBOSITY,
        use_custom_theme: bool = True,
        custom_theme: dict = None,
        enable_rich_tracebacks: bool = True,
        traceback_extra_lines: int = 3,
        show_local_vars_in_traceback: bool = False,
        display_time: bool = True,
        display_log_level: bool = True,
        time_format: str = DEFAULT_TIME_FORMAT,
        log_to_file: bool = False,
        log_directory: str = "logs",
        log_to_current_directory: bool = False
    ):
        self.verbosity = verbosity
        self.use_custom_theme = use_custom_theme
        self.custom_theme = custom_theme if custom_theme else self.DEFAULT_THEME
        self.enable_rich_tracebacks = enable_rich_tracebacks
        self.traceback_extra_lines = traceback_extra_lines
        self.show_local_vars_in_traceback = show_local_vars_in_traceback
        self.display_time = display_time
        self.display_log_level = display_log_level
        self.time_format = time_format
        self.log_to_file = log_to_file
        self.log_directory = log_directory
        self.log_to_current_directory = log_to_current_directory

    def setup_logger(self) -> None:
        """
        Sets up the logger based on the specified configuration.
        """
        log_level = self.VERBOSITY_LEVELS.get(self.verbosity, LoggingLevel.INFO.value)
        self._remove_existing_loggers()
        self._setup_standard_logging(log_level)
        self._setup_rich_loguru_handler(log_level)
        if self.log_to_file:
            self._setup_file_logging(log_level)

    def _remove_existing_loggers(self) -> None:
        """
        Removes any existing logger handlers.
        """
        logger.remove()

    def _setup_standard_logging(self, log_level: str) -> None:
        """
        Configures the standard Python logging to use Loguru logger.
        """
        logging.basicConfig(handlers=[self.LoguruRedirectHandler()], level=log_level)
        logging.getLogger().handlers = [self.LoguruRedirectHandler()]

    def _setup_rich_loguru_handler(self, log_level: str) -> None:
        """
        Configures Loguru logger with Rich handler for enhanced logging output.
        """
        logger.add(
            RichHandler(
                console=Console(theme=self._set_logging_theme(self.use_custom_theme)),
                rich_tracebacks=self.enable_rich_tracebacks,
                tracebacks_extra_lines=self.traceback_extra_lines,
                tracebacks_show_locals=self.show_local_vars_in_traceback,
                show_time=self.display_time,
                show_level=self.display_log_level,
                enable_link_path=True,
            ),
            format="{message}",
            level=log_level
        )

    def _setup_file_logging(self, log_level: str) -> None:
        """
        Configures Loguru logger to also log to a file.
        """
        if self.log_to_current_directory:
            log_directory = Path.cwd() / "logs"
        else:
            script_dir = Path(__file__).resolve().parent
            log_directory = script_dir / self.log_directory

        log_directory.mkdir(parents=True, exist_ok=True)
        computer_name = socket.gethostname()
        script_name = Path(__file__).stem
        log_file_path = log_directory / f"{computer_name}_{script_name}.log"

        logger.add(
            log_file_path,
            rotation="1 week",
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}",
            enqueue=True,
            serialize=False,
            mode="w",
        )

    def _set_logging_theme(self, use_override: bool) -> Theme:
        """
        Defines the theme for the logger.
        """
        return Theme(self.custom_theme) if use_override else Theme()

    class LoguruRedirectHandler(logging.Handler):
        """
        Intercepts standard logging messages and redirects them to loguru.
        """
        def emit(self, record):
            try:
                level = logger.level(record.levelname).name
            except ValueError:
                level = record.levelno

            frame, depth = logging.currentframe(), 2
            while frame and frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1

            logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())


# CONFIG LOADING
# =======================================================

def load_config(file_path):
    """
    Load configuration from a JSON or YAML file.
    If the file includes a 'files' list, it can be used for batch processing.
    """
    with open(file_path, 'r', encoding='utf-8') as file:
        if file_path.endswith('.json'):
            return json.load(file)
        elif file_path.endswith('.yaml') or file_path.endswith('.yml'):
            return yaml.safe_load(file)
        else:
            raise ValueError("Unsupported configuration file format. Use .json or .yaml")


# AUDIO TRANSCRIPTION
# =======================================================

def convert_to_wav(input_file):
    """
    Convert audio file to WAV format using pydub.
    """
    audio = AudioSegment.from_file(input_file)
    wav_file = input_file.rsplit('.', 1)[0] + '.wav'
    audio.export(wav_file, format="wav")
    return wav_file

def transcribe_audio(input_file, model_name, language=None, temperature=0.0, segment_duration=30):
    """
    Transcribe the given audio file using the specified Whisper model.
    Splits the audio into segments (default: 30 seconds) for incremental transcription.
    """
    model = whisper.load_model(model_name)
    if not model:
        return None

    was_converted = False
    # Convert audio to WAV format if it is not already .wav
    if not input_file.endswith('.wav'):
        input_file = convert_to_wav(input_file)
        was_converted = True

    try:
        # Load audio
        audio = whisper.load_audio(input_file)
        audio_length = len(audio) / whisper.audio.SAMPLE_RATE

        # Segment audio
        segments = np.array_split(audio, max(1, int(audio_length / segment_duration)))

        transcription = ""

        # Create a tqdm progress bar
        with tqdm(total=audio_length, unit='s', desc="Transcribing") as pbar:
            for segment in segments:
                segment_result = model.transcribe(
                    segment, language=language, temperature=temperature
                )
                transcription += segment_result['text']
                pbar.update(len(segment) / whisper.audio.SAMPLE_RATE)

        # Remove the temporary WAV file if it was newly created
        if was_converted and os.path.exists(input_file):
            os.remove(input_file)

        return transcription
    except Exception as e:
        logger.error(f"Error during transcription of {input_file}: {e}")
        return None


# MAIN SCRIPT / CLI
# =======================================================

console = Console()
logger_config = LoggerConfiguration(
    verbosity="quiet",
    use_custom_theme=True,
    enable_rich_tracebacks=True,
    traceback_extra_lines=3,
    show_local_vars_in_traceback=False,
    display_time=True,
    display_log_level=True,
    time_format="[%X]",
    log_to_file=True
)
logger_config.setup_logger()

def parse_arguments():
    """
    Parse command-line arguments with integrated prompting for missing values,
    and detect whether we should do batch mode from config (if 'files' key is present).
    """
    valid_models = [
        'tiny', 'base', 'small', 'medium', 'large',
        'tiny.en', 'base.en', 'small.en', 'medium.en',
        'large-v1', 'large-v2', 'large-v3'
    ]
    valid_languages = ['english', 'norwegian', 'french', 'spanish', 'german']
    parser = argparse.ArgumentParser(description="Transcribe audio/video files to text using Whisper.")
    parser.add_argument('input_files', type=str, nargs='*', help="Paths to the input files")
    parser.add_argument('-o', '--output_dir', type=str, help="Directory to save the transcribed texts")
    parser.add_argument('--model', type=str, default='medium', choices=valid_models,
                        help="Whisper model to use (tiny, base, small, medium, large, etc.)")
    parser.add_argument('--config', type=str, help="Path to configuration file (JSON or YAML)")
    parser.add_argument('--prompt', action='store_true', help="Prompt the user for input values")
    parser.add_argument('--debug', action='store_true', help="Enable debug mode for more verbose output")
    parser.add_argument('--language', type=str, choices=valid_languages, help="Language of the audio")
    parser.add_argument('--temperature', type=float, default=0.0, help="Temperature for transcription")
    args = parser.parse_args()

    # Default to single-file mode
    args.batch_mode = False
    args.batch_files = []

    # If a config file is provided, try loading it
    if args.config:
        config = load_config(args.config)

        # If 'files' key exists in the config, treat it as a batch scenario
        if 'files' in config:
            args.batch_mode = True
            args.batch_files = config['files']
            # Pull global-level config settings if provided
            args.output_dir = config.get('output_dir', args.output_dir)
            args.model = config.get('model', args.model)
            args.language = config.get('language', args.language)
            args.temperature = config.get('temperature', args.temperature)
        else:
            # Otherwise, proceed with single-file approach but override defaults if present
            args.input_files = config.get('input_files', args.input_files)
            args.output_dir = config.get('output_dir', args.output_dir)
            args.model = config.get('model', args.model)
            args.language = config.get('language', args.language)
            args.temperature = config.get('temperature', args.temperature)

    return args

def clear_console():
    """
    Clears the terminal screen for Windows, Linux, or macOS.
    """
    if platform.system() == "Windows":
        os.system("cls")
    else:
        os.system("clear")

def ensure_directory_exists(directory: Path):
    """
    Ensure the output directory exists or create it.
    """
    directory.mkdir(parents=True, exist_ok=True)
    logger.info(f"Created (or found existing) output directory: {directory}")

def display_summary(args, transcription):
    """
    Display a summary of the configuration settings and partial transcription in a Rich table.
    """
    table = Table(
        title="Configuration Summary",
        show_header=True,
        header_style="bold magenta",
        box=box.ASCII
    )
    table.add_column("Parameter", style="dim", width=20)
    table.add_column("Value", style="bold cyan")

    # Show either the batch file list or the single input files
    if args.batch_mode:
        file_list = [item['file_path'] for item in args.batch_files]
        table.add_row("Input Files (Batch)", ", ".join(file_list))
    else:
        table.add_row("Input Files", ", ".join(args.input_files) if args.input_files else "None")

    table.add_row("Output Directory", args.output_dir or "Not specified")
    table.add_row("Model", args.model or "Not specified")
    snippet = transcription[:100] + "..." if len(transcription) > 100 else transcription
    table.add_row("Transcription", snippet)
    console.print(table)
    logger.info("Displayed configuration summary.")

def resolve_paths(input_files, working_directory):
    """
    Resolve relative file paths to absolute paths based on the working directory.
    Also strips quotes if provided in the CLI.
    """
    resolved_files = []
    for file in input_files:
        file = file.strip('\"\'')
        path = Path(file)
        if not path.is_absolute():
            path = working_directory / path
        resolved_files.append(str(path))
    return resolved_files

def transcribe_and_save(input_file, output_dir, model, language, temperature):
    """
    Transcribe a single file and save the resulting text to the specified output directory,
    using UTF-8 to accommodate all possible Unicode characters.

    Now also SKIPS if a .txt with the same name already exists in output_dir.
    """
    path_obj = Path(input_file)
    output_file = Path(output_dir) / (path_obj.stem + '.txt')

    # If the transcription already exists, skip
    if output_file.exists():
        console.print(f"Skipped transcription (file already exists): [bold cyan]{output_file}[/bold cyan]")
        logger.info(f"Skipped transcription for {input_file} - {output_file} already exists.")
        return

    if not path_obj.is_file():
        console.print(f"The specified input file does not exist:\n{input_file}", style="bold red")
        logger.error(f"The specified input file does not exist: {input_file}. Skipping.")
        return

    start_time = datetime.now()
    transcription = transcribe_audio(str(path_obj), model, language, temperature)
    if transcription is None:
        logger.error(f"Transcription returned None for file: {input_file}")
        return

    ensure_directory_exists(output_file.parent)

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(transcription)

    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()

    console.print(f"Transcription saved to: [bold cyan]{output_file}[/bold cyan] (Duration: {duration:.2f}s)")
    logger.info(f"Transcription saved to: {output_file} (Duration: {duration:.2f}s)")
    return output_file

def main():
    args = parse_arguments()
    if args.debug:
        logger_config.verbosity = "verbose"
    logger_config.setup_logger()

    working_directory = Path.cwd()

    # ------------------------------------------------------------------
    # INTERACTIVE PROMPT: Ask if user wants to load a config file on the fly
    # (only if --prompt is specified AND they haven't already used --config).
    # ------------------------------------------------------------------
    if args.prompt and not args.config:
        load_config_answer = Prompt.ask(
            "Load a YAML/JSON configuration file? [y/n]",
            default="n"
        ).lower()

        if load_config_answer == 'y':
            config_path = Prompt.ask("Enter the path to your config file")
            if config_path and Path(config_path).is_file():
                config_data = load_config(config_path)
                if 'files' in config_data:
                    args.batch_mode = True
                    args.batch_files = config_data['files']
                    args.output_dir = config_data.get('output_dir', args.output_dir)
                    args.model = config_data.get('model', args.model)
                    args.language = config_data.get('language', args.language)
                    args.temperature = config_data.get('temperature', args.temperature)
                else:
                    args.input_files = config_data.get('input_files', args.input_files)
                    args.output_dir = config_data.get('output_dir', args.output_dir)
                    args.model = config_data.get('model', args.model)
                    args.language = config_data.get('language', args.language)
                    args.temperature = config_data.get('temperature', args.temperature)
            else:
                console.print("[bold yellow]Config path not found or invalid. Continuing without config.[/bold yellow]")

    # ------------------------------------------------------------------
    # If in batch mode, skip single-file prompts entirely.
    # ------------------------------------------------------------------
    if args.batch_mode:
        batch_tasks = args.batch_files
        output_dir = args.output_dir or (working_directory / "output")

        # -- SINGLE-THREAD EXECUTION (no ThreadPoolExecutor) --
        for item in batch_tasks:
            try:
                file_path = item['file_path']
                model = item.get('model', args.model)
                language = item.get('language', args.language)
                temperature = float(item.get('temperature', args.temperature))

                transcribe_and_save(
                    input_file=file_path,
                    output_dir=output_dir,
                    model=model,
                    language=language,
                    temperature=temperature
                )
            except Exception as e:
                console.print(f"An error occurred during batch transcription: {e}", style="bold red")
                logger.error(f"An error occurred during batch transcription: {e}")

        display_summary(args, "Batch transcription completed for all files")

    else:
        # --------------------------------------------------------------
        # SINGLE-FILE FLOW
        # --------------------------------------------------------------
        default_input_files = args.input_files if args.input_files else [working_directory / "audioclip.mp3"]
        default_output_dir = args.output_dir if args.output_dir else (working_directory / "output")
        default_model = "base"
        valid_languages = ['english', 'norwegian', 'french', 'spanish', 'german']
        default_language = "english"
        default_temperature = "0.0"

        while True:
            clear_console()

            if args.prompt:
                input_files_prompt = Prompt.ask(
                    "Input files (space-separated, use quotes if paths contain spaces or commas)",
                    default=" ".join(f'"{file}"' for file in default_input_files)
                )
                try:
                    input_files = shlex.split(input_files_prompt)
                except ValueError as e:
                    console.print(f"Error parsing input files: {e}", style="bold red")
                    logger.error(f"Error parsing input files: {e}")
                    continue

                args.input_files = resolve_paths(input_files, working_directory)
                args.output_dir = Prompt.ask("Output directory", default=str(default_output_dir))
                args.model = Prompt.ask(
                    "Model to use (tiny, base, small, medium, large, tiny.en, base.en, etc.)",
                    default=default_model
                )
                args.language = Prompt.ask(
                    "Language of the audio",
                    choices=valid_languages,
                    default=default_language
                )
                args.temperature = Prompt.ask("Temperature for transcription", default=default_temperature)
            else:
                # If user isn't prompted, just resolve pre-existing input files
                args.input_files = resolve_paths(args.input_files, working_directory)

            print(f"Resolved input file paths: {args.input_files}")

            # -- SINGLE-THREAD EXECUTION FOR SINGLE-FILE MODE --
            for input_file in args.input_files:
                try:
                    transcribe_and_save(
                        input_file=input_file,
                        output_dir=args.output_dir or str(default_output_dir),
                        model=args.model,
                        language=args.language,
                        temperature=float(args.temperature)
                    )
                except Exception as e:
                    console.print(f"An error occurred during transcription: {e}", style="bold red")
                    logger.error(f"An error occurred during transcription: {e}")

            display_summary(args, "Transcription completed for all files")

            if not Confirm.ask("Do you want to transcribe more files?", default="no"):
                break


if __name__ == "__main__":
    main()

```
