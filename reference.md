{"cells": [{"cell_type": "markdown", "id": "9c06a20a", "metadata": {}, "source": ["<center><h1>TRANSCRIBING AN AUDIO FILE</h1></center>\n", "\n"]}, {"cell_type": "markdown", "id": "85c96f0e", "metadata": {}, "source": ["<center><h1>Reference python codes in Jupyter using  OpenAi's  Speech to Text model (Whisper-1)</h1></center>"]}, {"cell_type": "markdown", "id": "16159f6f", "metadata": {}, "source": ["__<center>By : <PERSON><PERSON> (aka Analy<PERSON><PERSON>It)</center>__"]}, {"cell_type": "markdown", "id": "7dfdf9af", "metadata": {}, "source": ["<center>`***********************************************************************************************************`</center>"]}, {"cell_type": "markdown", "id": "7cf6bc70", "metadata": {}, "source": ["This guide is all about teaching you how to convert spoken words into written text. We'll be using  OpenAI's technology (Speech to Text engine- StT engine) to understand the spoken words. We will be using Python programming language to call the audio and convert it into a text via the StT engine. The pyton codes will be run into Jupyter Notebook.  This is more about showing you the steps rather than teaching you coding in python. We'll start by turning one audio recording into text and then show you how to do this with many recordings at the same time. In the end, we'll put all the written text together into one big piece. Think of this as a handy guide to use whenever you need to turn speech into text using OpenAI's tools and with a bit of Python knowledge."]}, {"cell_type": "markdown", "id": "f033b0c3", "metadata": {}, "source": ["The first thing you need to do is to import the open openai module and call your API in the notebook. \n", "\n", "Before importing the openai module  in your notebook,it must be al<PERSON><PERSON> installed into your computer via command prompt shell. If you have a window enviorment in your computer, go to the search and type cmd. Once your command prompt is opened,  type : \"pip install openai\" and press enter key. See illustration below."]}, {"attachments": {"pip%20picture.png": {"image/png": "..."}}, "cell_type": "markdown", "id": "7b65d577", "metadata": {}, "source": ["![pip%20picture.png](attachment:pip%20picture.png)"]}, {"cell_type": "markdown", "id": "c5cbd19a", "metadata": {}, "source": ["The line of code below is the Python statement that imports the openai module. This allows you to use the functionality provided by the openai package in your code. "]}, {"cell_type": "code", "execution_count": 1, "id": "0b42a19d", "metadata": {}, "outputs": [], "source": ["# Importing the OpenAI module \n", "from openai import OpenAI\n"]}, {"cell_type": "markdown", "id": "0129354f", "metadata": {}, "source": ["Then, provide the API key that is associated with your openai account. This  will be  used to authenticate and authorize access to an API (Application Programming Interface). APIs are sets of rules and protocols that allow different software applications to communicate with each other. In that case, it is between Python and OpenAI speech to text engine.  \n", "\n", "<font color=\"red\">&#9888; Be careful!</font>\n", ": Don't share your API key. It should be kept confidential to prevent unauthorized usage.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "e7034a52", "metadata": {}, "outputs": [], "source": ["# Call your openAI API key\n", "key = \"***************************************************\""]}, {"cell_type": "code", "execution_count": 3, "id": "339e8559", "metadata": {}, "outputs": [], "source": ["client = OpenAI(api_key = key)"]}, {"cell_type": "markdown", "id": "17d7ec5c", "metadata": {}, "source": ["To get an OpenAI API key you need to create an OpenAI Account. Visit the OpenAI website and sign up for an account if you don't already have one. Once logged in, navigate to the API section. This is typically found in your account dashboard or under a dedicated API section on the website. Click on \"Create a new secret key\".  Copy and save it because once you go back into you account, it will not anymore be displayed. "]}, {"attachments": {"Screenshot-openai.png": {"image/png": "..."}}, "cell_type": "markdown", "id": "8b4f702f", "metadata": {}, "source": ["![Screenshot-openai.png](attachment:Screenshot-openai.png)"]}, {"cell_type": "markdown", "id": "aade5538", "metadata": {}, "source": ["## Reading and transcribing a single audio file "]}, {"cell_type": "markdown", "id": "9e3eada2", "metadata": {}, "source": ["Once you have imported the openai module and provided the api key, you should provide the path where your audio is located or stored. In this example, I have an audio file named \"yann\" in the format of an mp3 and it  is located in my desktop. As of today (December 2023), openai's TtS model (Whisper-1) can process audio file in the format of mp3, mp4, mpeg, mpga, m4a, wav, and webm.\n", "\n", "By default, the Whisper API only supports files that are less than 25 MB. If you have an audio file that is longer than that, you will need to break it up into chunks of 25 MB's or less or used a compressed audio format. To get the best performance, avoid breaking the audio up mid-sentence as this may cause some context to be lost."]}, {"cell_type": "code", "execution_count": 4, "id": "c85ec46e", "metadata": {}, "outputs": [], "source": ["# create a variable that provides the path of your audio file \n", "audio_path = \"C:/Users/<USER>/Desktop/yann.mp3\""]}, {"cell_type": "markdown", "id": "935156d5", "metadata": {}, "source": ["Now let's reads the audio file, sends it to the OpenAI API for transcription using language model (\"whisper-1\"). And then, we will print the resulting transcription to the console. In the codes we also ensure that if any errors occur during this process, they are caught and an error messages is displayed. Here is a break down of each steps for the next block of codes: \n", "\n", "__Try-Except Block__:The code is enclosed within a try and except block. This is a common structure used in Python for handling exceptions (errors). It means that the code within the try block will be executed, and if any exceptions (errors) occur during execution, they will be caught and handled in the except block. This structure helps prevent the script from crashing due to errors and allows for graceful error handling.\n", "\n", "\n", "__Opening the Audio File__: In the with statement, the code opens an audio file specified by the audio_path variable in binary read mode (\"rb\"). The with statement is used to ensure that the file is properly closed after it's done being used. This is a good practice to avoid resource leaks.\n", "\n", "\n", "__Transcribing the Audio File__: Inside the with block, the code calls the OpenAI API to transcribe the audio file. Here are the details of the parameters used:\n", "    \n", "> _model_: Specifies the language model to be used for transcription. In this case, it's set to \"whisper-1,\" which is a specific model trained for transcription.\n", "\n", "> _file_: Specifies the audio file to be transcribed. The audio_file variable contains the opened audio file.\n", "\n", "> _response_format_: Specifies the format in which you want the transcription response. Here, it's set to \"text,\" which means you want the transcription as plain text.\n", "\n", "> _language_: Specifies the language of the audio content. It's set to \"en,\" indicating English language content.\n", "\n", "\n", "__Storing the Transcription__: The result of the transcription is stored in the transcript variable.\n", "\n", "\n", "__Printing the Transcript__:After transcribing the audio, the script prints the transcript to the console using print(transcript).\n", "\n", "\n", "__Exception Handling__:If any exceptions (errors) occur during the execution of the try block, they will be caught in the except block. The exception object is assigned to the variable e, and the code prints an error message indicating that an error occurred along with the details of the error (e.g., the error message).\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "id": "9b5d1592", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["What is your name? My name is <PERSON><PERSON><PERSON> How old are you? I'm 19 years old What are you doing right now? I'm currently enrolled in college studying to become a computer science major What you would like to do in the future? I would want to learn cyber security And what? And learn how to code, program and just software development Alright, thank you\n", "\n"]}], "source": ["try:\n", "    # Open the audio file\n", "    \n", "    with open(audio_path, \"rb\") as audio_file:\n", "        \n", "        # Transcribe the audio file\n", "        transcript = client.audio.transcriptions.create(\n", "            model=\"whisper-1\",\n", "            file=audio_file,\n", "            response_format=\"text\",\n", "            language=\"en\"\n", "        )\n", "    # Print the transcript\n", "    \n", "    print(transcript)\n", "    \n", "except Exception as e:\n", "    print(\"An error occurred:\", e)"]}, {"cell_type": "markdown", "id": "4a78db4e", "metadata": {}, "source": ["## Reading and transcribing multiple  audio files simultenaously "]}, {"cell_type": "markdown", "id": "35eee688", "metadata": {}, "source": ["To transcribe multiple audio files simultenaously, you can creat a script that will iterate over a list of audio file paths, transcribes each file into text using OpenAI's transcription service, and stores each resulting transcription in a dictionary with a distinct key. This will allow for easy access and management of the transcribed texts.\n", "\n", "First you create a variable 'audio_paths' which  is a list containing the file paths of the audio files you want to transcribe. In this example, there are two file paths pointing to .mp3 files on my desktop."]}, {"cell_type": "code", "execution_count": 6, "id": "b2f6fb66", "metadata": {}, "outputs": [], "source": ["# List of paths to your audio files\n", "audio_paths = [\n", "    \"C:/Users/<USER>/Desktop/yann.mp3\",\n", "    \"C:/Users/<USER>/Desktop/yann1.mp3\"\n", "    # Add more paths as needed\n", "]"]}, {"cell_type": "markdown", "id": "059019b6", "metadata": {}, "source": ["Create a function that will contain the codes used to transcribe a single audio file (as we did above). In our example below, it is named  'transcribe_audio' and it will be called while iterating over each audio file path. "]}, {"cell_type": "code", "execution_count": 7, "id": "57e4d198", "metadata": {}, "outputs": [], "source": ["# Function to transcribe a single audio file\n", "def transcribe_audio(file_path):\n", "    try:\n", "        with open(file_path, \"rb\") as audio_file:\n", "            transcript = client.audio.transcriptions.create(\n", "                model=\"whisper-1\",\n", "                file=audio_file,\n", "                response_format=\"text\",\n", "                language=\"en\"\n", "            )\n", "        return transcript\n", "    except Exception as e:\n", "        print(f\"An error occurred while transcribing {file_path}: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "id": "6eb1ac14", "metadata": {}, "source": ["Create a variable transcriptions which is a dictionary that will store the transcriptions. Each transcription will be stored with a unique key so that it can be easily referenced later."]}, {"cell_type": "code", "execution_count": 8, "id": "b29eeaaa", "metadata": {}, "outputs": [], "source": ["# Dictionary to store each transcription with a distinct name\n", "transcriptions = {}"]}, {"cell_type": "markdown", "id": "69ac8c66", "metadata": {}, "source": ["Now that you have created  a list that contain the path for your audios and you have created a function that can transcribe an audio file, you will itterate over each audio file, transcribe it, and store the result in the dictionary.\n", "\n", "Here is a break down explaination of each steps and/or codes used:\n", "\n", "* The 'for' loop iterates over each audio file path in the 'audio_paths' list.\n", "\n", "\n", "* 'enumerate(audio_paths)' provides both the index ('i') and the path ('path') of each audio file in the list.\n", "\n", "\n", "* variable_name = f\"transcription_{i}\": For each audio file, a unique variable name is created to store its transcription. The name is formed by combining the word \"transcription\" with the index of the file in the list ('i').\n", "\n", "\n", "* The script then prints a message indicating it's transcribing the current file.\n", "\n", "\n", "* transcription = transcribe_audio(path): The transcribe_audio function is called with the current audio file path, and the returned transcription is stored in 'transcription'.\n", "\n", "\n", "* If the transcription is successful (not 'None'), it is added to the transcriptions dictionary with the unique key created earlier ('variable_name').\n", "\n", "\n", "\n", "* A message is printed to indicate that the transcript for the current file has been stored successfully in the dictionary with the specific 'variable_name'.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "36693391", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transcribing: C:/Users/<USER>/Desktop/yann.mp3\n", "Transcript for C:/Users/<USER>/Desktop/yann.mp3 stored in variable 'transcription_0'\n", "Transcribing: C:/Users/<USER>/Desktop/yann1.mp3\n", "Transcript for C:/Users/<USER>/Desktop/yann1.mp3 stored in variable 'transcription_1'\n"]}], "source": ["# Iterate over each audio file, transcribe it, and store the result in the dictionary\n", "for i, path in enumerate(audio_paths):\n", "    variable_name = f\"transcription_{i}\"\n", "    print(f\"Transcribing: {path}\")\n", "    transcription = transcribe_audio(path)\n", "    if transcription:\n", "        transcriptions[variable_name] = transcription\n", "        print(f\"Transcript for {path} stored in variable '{variable_name}'\")"]}, {"cell_type": "markdown", "id": "a1933e90", "metadata": {}, "source": ["Now you can access each transcription by its variable name from the dictionary.  For example, the code below will print the transcription of the second file. "]}, {"cell_type": "code", "execution_count": 10, "id": "06e4e3c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["What is your name? My name is <PERSON>. How old are you? I am 53 years old. What are you doing right now? I'm currently working for an INGOs. What would you like to do in the future? In the future I would like to learn more about artificial intelligence and programming. And what? I also would like to develop some software to help monitoring and evaluation. All right, thank you very much for your interview.\n", "\n"]}], "source": ["print(transcriptions[\"transcription_1\"])  "]}, {"cell_type": "markdown", "id": "a0bc19e1", "metadata": {}, "source": ["Alternatively,  for each  audio transcripted, you can recreate a variable that store your audio transcript in format of a string that you can reference for preprocessig and for applying in-depth NLP algorithm. \n", "\n", "For example, in the codes below we will concatenate each trancription into a single variable.The result will be a string variable on which you can perform preprocessing and apply Natural Language Processing alogrithm for analyzing the text. "]}, {"cell_type": "code", "execution_count": 11, "id": "11b74a4e", "metadata": {}, "outputs": [], "source": ["audio1 = transcriptions[\"transcription_0\"]\n", "audio2 = transcriptions[\"transcription_1\"] "]}, {"cell_type": "code", "execution_count": 12, "id": "fd7001a2", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"What is your name? My name is <PERSON><PERSON><PERSON> How old are you? I'm 19 years old What are you doing right now? I'm currently enrolled in college studying to become a computer science major What you would like to do in the future? I would want to learn cyber security And what? And learn how to code, program and just software development Alright, thank you\\nWhat is your name? My name is <PERSON>. How old are you? I am 53 years old. What are you doing right now? I'm currently working for an INGOs. What would you like to do in the future? In the future I would like to learn more about artificial intelligence and programming. And what? I also would like to develop some software to help monitoring and evaluation. All right, thank you very much for your interview.\\n\""]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["audio1_2 = audio1 + audio2\n", "audio1_2"]}, {"cell_type": "markdown", "id": "a7e1cfd3", "metadata": {}, "source": ["## Saving a String to a Word Document"]}, {"cell_type": "code", "execution_count": 13, "id": "79727f84", "metadata": {}, "outputs": [], "source": ["from docx import Document"]}, {"cell_type": "code", "execution_count": 14, "id": "7c036b30", "metadata": {}, "outputs": [], "source": ["doc = Document()"]}, {"cell_type": "code", "execution_count": 15, "id": "9778e138", "metadata": {}, "outputs": [{"data": {"text/plain": ["<docx.text.paragraph.Paragraph at 0x1d079dda9a0>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["doc.add_paragraph(audio1_2)"]}, {"cell_type": "code", "execution_count": 16, "id": "fc37301e", "metadata": {}, "outputs": [], "source": ["doc.save(\"C:/Users/<USER>/Desktop/audio1_2.docx\")"]}, {"cell_type": "code", "execution_count": null, "id": "eee00fe7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 5}