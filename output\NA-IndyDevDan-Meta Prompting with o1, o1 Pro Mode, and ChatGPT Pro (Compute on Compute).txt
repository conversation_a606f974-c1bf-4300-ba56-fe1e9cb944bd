 What's up, engineers? Welcome back. I have a massive two-for-one grand slam for you today. OpenAI's 12 days of releases has kicked off with ChatGPT Pro and the new complete 01 model. I absolutely had to purchase the insane ChatGPT Pro subscription for 200 bucks a month. I bought this because of course I'm addicted to being on the edge and also so you don't have to take on the risk before you see what it can do. In this video you'll find out... if it's worth it. We've been preparing for the O1 launch for quite a while. The trick with these highly capable reasoning models like O1 and O1 Pro Mode is knowing what to prompt to reveal and challenge its capabilities. And oh boy, do I have a challenge for O1 today? We're challenging the new O1 series with Meta Prompting. In advanced prompt engineering technique, where you write a prompt that writes prompts for you, this technique unlocks asymmetric productivity for you and the generative AI age that we live in. Let's talk about why and how to best utilize this pattern in this video. Let's understand metaprompting with hands-on examples using the new O1 and O1 Pro mode. And let's see if it's worth your hard-earned cash. This is a metaprompt. At first glance, this just looks like a standard information rich XML prompt with a huge set of instructions. You can see this prompt has a 3000 tokens. So there's a lot going on here. There's a lot of information packed into this prompt. Let's run our MetaPrompt in a static way by replacing our user input variable here. We'll generate prompts using the new 01 model and then we'll take those prompts and run it against both chat GPT 01 and 01 Pro mode at the same time so we can compare the results side by side together. So let's start with some MetaPrompting. In our directory here you can see we have have three examples that we're going to work through and some images that we'll talk about in a second. Let's go ahead and start out by just generating three new prompts from our Metapromp. So what I'll do is I'll copy the Metaprompt, just paste it in a brand new file here, and then let's start with our hacker news perspective. So this is a simple user input where I'm basically walking through everything we want to pass into this prompt. I'm using a semi-structured input format. You can see we have purpose, instructions, and variables. And all I'll do here is copy this, paste it into our Meta prompt and replace our user input. I'll then copy the entire Meta prompt and go over to chat GBT using the new 01 model. I'm just going to paste this in and we're going to let this rip. So we want 01 to generate a new prompt based on this Meta prompt. And all the details for what the prompt will do is embedded in the user input here. So you can see it took nine seconds for 01 to spit out this result. This is much faster over. or one preview, we can go ahead and just copy this. Let's create a new file here. We'll call this prompt hnperspectives. We're gonna use this prompt to analyze hacker news commentary. We have this new prompt here. This is pretty incredible. It's generated automatically for us. Thanks for our meta prompt in an XML-ish format. We've talked about XML format on the channel in the past. Long story short there is that XML is the best way to get high quality results out of your prompts. Feel free to pause the video and check out the details of this prompt. We'll do that later. basically just took everything we asked for in our user input. It read the entire meta prompt that we have here. Tons of instructions here, we have an input format and then of course several rich examples and it generated a brand new prompt for us. So you can see here you're an expert at analyzing and aggregating perspectives from a given hacker news post. So great start, solid instructions here. Let's continue to our next prompt bug analysis. This is a prompt that's going to analyze code and report bugs. So as you'll see, this is going to be a very useful prompt for reviewing code. Why have your coworker review your code? We're going to have AI review it first and save them and yourself a lot of time in the PR review process. So this is a powerful prompt. You can see here we actually have a example of what we want the examples in the prompt to look like. So we're giving them metaprompt, some additional information here to create a great IQuality prompt. To solve the problem we're aiming to solve. In this case, we want to analyze and review code. Fixes to be recommended. We want it to look. for critical bugs that will crash the program. We also wanted to give us a ranking. So when you're writing the input to the metaprompt that defines the prompt you're looking for, you can just kind of write out, you know, in natural language, what do you want the sections to look like? What are the variables? What are the sections? What do you want your examples look like? Copy this and we can replace the previous user input here. Copy this and same deal. So I'm going to start a new chat here, paste this in and run this once again on 01. We don't need 01 the real comparison will be in executing these large intense prompts in O1 Pro mode. So let's go ahead and copy this out. You can see we have a great prompt written here. Let's go ahead and take a look at this. So this is prompt bug analysis. If we paste this in, you know, we have three examples. So these were automatically generated examples thanks to our meta prompt. You can see they format, you know, it's giving us severity, the file, the description, and then something cool we add to the example we want. a control F lookup to figure out where that issue is and it's gonna give us a recommended fix. So this is, you know, the importance of good prompt engineering. You're never gonna get this format by just saying find bugs in my code, right? By doing something like this, right? Find bugs in my code and then you just place your code here, right? This is why prompt engineering is a very, very real skill by specifying examples, by creating instructions, by defining a clear purpose. This prompt output is going to be, Very very rich and useful as you'll see in a second here. So this is great We have a bug analysis prompt. Let's go ahead and generate one more prompt Here's our last prompt here, and this is gonna be a let me actually create this update this to XML here And our last prompt here is going to be a script to blog We're gonna take the script from a previous YouTube video and then we're gonna pass in the topic or the title of that video And it's going to generate a html CSS only blog based on the script okay, so we're just gonna copy this And then we're gonna create a script for the script to go to the script and then we're gonna create a script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for the script for Follow that same process. We're going to replace our user input from our Meta prompt. And, you know, I just have these all pre-tucked out and I want to waste your time writing out these user inputs. So I'm just going to copy this. We're going to start a new session so that the chat history does not affect the Meta prompt. And, you know, let's actually go ahead and use 01 Pro Mode for this final Meta prompt. I'm going to run that here and we can do this side by side just for fun. So we'll do this side by side. You can see a big difference here. 01 Pro Mode is taking some time to think right away. And you can see, oh, one, the O1 base model is already done. It's written this great instruction rich prompt. It has the two variables I asked for. It has the lead in for the completion, for the LLM that's going to run this prompt. This is quite fantastic, right? You're an expert web designer and writer who specializes in creating engaging human-like blog posts using only HTML and CSS, okay? So right away we can see just... by running our Metapromps, O1 is very, very powerful. I'm still kind of in shock that this ran for four seconds and it was able to work through the Metapromp that quickly. So this is a task that GPT-4O level models typically have problems with. If you look at the Metapromp, it's kind of complex, especially when you get to the examples because in the examples, we have input prompts and the expected outputs, which are themselves prompts. So you can imagine. lower quality even middle tier models have problems with the meta prompt and you know we can see the new 01 did this task with no problem in four seconds okay so let's go ahead and just use this I'll say prompt crypt to blog.xml file I'll paste this and now we have a new prompt right this new generative AI mini program that takes in a script and a topic and... will generate an HTML and CSS blog for us. We'll see how the outputs perform between 01 Pro mode and 01 in just a moment. But you can see here we have a very, very similar output from 01 Pro mode. For the use case of metaprompting, 01 and 01 Pro mode put out very similar results. You can see avoid overhype and keep it real, avoid sounding like an AI, aim for natural human-like voice. Actually prefer the 01 output here a little bit more. For that specific... but very, very similar outputs here. We're not going to worry about this. This is not really what we were aiming to compare. Let's go ahead and open up new sessions and start running our prompts that are Metaprops generated for us. Before we move on, let me just quick highlight again the value of Metaprompting. Why is Metaprompting so important? If you haven't noticed, the prompt is everything. Engineers, product builders, managers, anyone who can get their hands on a high quality prompt. can do a lot more with a lot less. Props give you super human abilities, thanks to the language models underneath them. As every engineer with their eyes actually open is noticing, you know, software engineering has completely changed, right? Language models prompting has completely changed the way software is written. This is just a single domain. There are many others actively being transformed by LLMs, prompts, and gen AI as a whole, right? There are many more to comment. They're all fueled by one thing, the prompt. In the age of generative AI, your ability to generate prompts is your ability to generate results. So the quality and the quantity of prompts, right? Highlighting here, the metaprompt. The quality and quantity of prompts you can generate will determine how effective you are in the generative AI age. That's what metaprompting is all about. More prompts at higher qualities based on your metaprompt. You can start with the one we're experimenting. with right here. I've been using this metaprompt here for about half a year now. 3K tokens, great clear instructions, some solid examples. You can start with the metaprompt here. The link will be in the description. This is a very, very valuable resource and an important pattern. I'm giving to you here and it's going to become more important especially when we embed our metaprompt into AI agents that can then automate the process of executing the metaprompt which generates. do prompts for us, hint, hint, more on that coming in 2025. You can really hear the agentic recursive, auto looping nature of handing off this metaprompt to an AI agent, right? So, smash the like, comment, subscribe, stay connected to this information, and be on the lookout for the weekly video every Monday. In 2025, everything we're doing here is going parabolic, especially as the technology of generative AI continues to evolve. So, that's the important of the metaprompt. Let's go ahead and run it. Let's make sure that you know, it's actually generating some great equality prompts for us. I Thought it would be interesting and cool to actually look at the hacker news post on chat GPT for this We're going to paste in a block of JSON which contains all the kind of comments in the commentary We can go to the post here. So here's the you know chat GPT pro hacker news post and you know a bunch of rich commentary No AI summary will ever replace the learning and the growth you'll have when you actually just read everyone's thoughts and opinions. So, you know, highly recommend you do that. We can go ahead take this post ID and go to Algolya and paste that here and, you know, with this link, what you'll see is the entire Hacker News post broken down in this JSON format. So we can copy this. I'll create a new file as hnpost.json. Paste the results in here. Well, automatic format. You can see this is a 200k token JSON. blob. That is too many tokens. I think that 01 and 01 Pro mode run with about a max of 128 K tokens, which is still quite a lot. So let's go ahead and pair this down a little bit. We can open up the terminal and use JQ for this. So if I run JQ dot children and then we pass an H and post, you can see all the children getting printed out there and you can see all the comments are going to be in this first children block. Okay. So this is all we need to get all the high-connews comments. And if we hit up again and we do a length check on this, I think it's just pipe length. Yeah. So we can see we have 145 items. Let's pair this down to 50. So we'll get the first 50. And I want to output this to hnpost50.json. OK. And then we can just look at this new file. That's 100K tokens. This is going to be more manageable. So it's kind of wild to say 100K tokens. It's going to be more manageable. but it's true. That's how powerful these O1 models are. So we're going to copy this. I'll open up our prompt HN perspectives. I'll paste this in and I'll actually copy this into a new file here or over this. And now we have our prompt HN perspectives prompt filled out. So if I go back to XML here, you can see we have user prompt with all of that JSON. And now we have a 100K token prompt. This is the moment of truth. We can open up .tptpro instances here. Let's make sure we're in fresh. sessions and let's see how both of these models perform. Okay, same prompt, both sides, O1 Pro mode, O1, and let's fire these off. So right away, first thing to notice is that these models are thinking a little bit and you can see how long this scroll takes. This is a huge, huge prompt. Right away, they're thinking, okay, O1 is outputting results already. This is very, very impressive. It thought for one second. Wow, okay, so they really... really really punch this model forward. O1 is very fast. I mean, that's non-reasing models are slower than this. So very impressive. You can see on the left side, O1 Pro mode, if you open up the details, actually doesn't have any details here for us yet, it's thinking. So that's fine, but you can see, you know, it's taking some time. This is the, you know, typical UI for O1 Pro mode. You paste something in and then it takes some time to really think through. Meanwhile, we can work through our output on... Okay, okay, very... Oh, one parlor just wrapped up for us and it just spit out this massive result. So this is fantastic. It thought for one minute, you can see that here. Let's kind of walk through this, right? So main points, perspectives from the discussion. So we have price value justification, debate over the 200 month price tag. Of course, that makes sense. Many users urge its expensive compared to 20. Of course, it's basically a 10x jump. I think the big kicker here, which I completely agree with, some users find it absurd. All others see it justified if it can give you the productivity. rates or if you can basically pay it back by saving time from using it, right? The idea is that if it saves you multiple hours on time monthly, it's worth it. For others, it's simply too expensive, right? So totally understandable. You can see that same type of perspective here broken down in O1 Pro mode. Comparison to alternatives is a big topic here. And you can see both Models kind of going down the line here. This all looks great. I do like the shorter more concise bullet points from O1 Pro mode but if we scroll down here and look for top three representative comments for each one of the perspectives. This was a great comment, right? If you save 4.5 hours in a Google sheet, the 200 bucks a month is cheap, right? I mean, that's paid itself off very, very quickly. So we have, you know, if you don't have the use case for O1 reasing models, which a lot of engineers and builders don't need the O1 model and also my opinion here is that most engineers and product builders don't know how to use these powerful reasoning models. You really do need. interesting, hard, complex problems to even get value out of these. Otherwise, 40, Gemini, Sonnet is going to cover most cases. But you can just kind of see here, we're going to get very similar results. I'm leaning more toward 01 Pro here, but these are where things get kind of interesting. We have a breakdown of the table. We have five different perspectives, we have sentiment, we have summaries, these are great. You can pause the video, read through these if you're interested. And then we have a nice flow chart here. So I actually want to copy out these flow charts. I did also ask for... flow chart at the end here, which is kind of hilarious to ask for that. And let's just open this up and take a look at this flow chart. Okay. So this is really interesting. So users assess this value, price debates, 200 is too high, cheap alternatives, logical flow here. Right. If you're using claw, open source, just skip it. Quality is uncertain. So hallucinates. So I don't know about this. This model doesn't really hallucinate. Yeah. If it saves time, if you're a high earner, if you have complex use cases, then it's completely justified, right? So you have business specifications, we could put the losses. So this is a decent chart, right? This kind of breaks down, I think, the sentiment around ChattityPotty Pro. So let's go ahead and look at the, as we're talking about it, let's go ahead and look at the results from ChattityPotty Pro 01 Pro mode. So let's just pace this in and let's see this breakdown, right? So Open Now Introduces Pro at 200 worth it. So I like this flow better. Yes, high-income pros justify time saving, less time spent on company. task worth it. No individuals, you know, too expensive, not enough value, secure cheaper alternatives, make sense. We have the open source block here, and then we have adoption and reliance, right? Addiction leads to potential higher price, skeptical LLM hype question. So all make sense. I love the breakdown from both of these models. Let's go ahead and close this up and move on to our next prompt. So far, how are you liking the output of O1 ProMboad and O1? As you can see, I think the difference is marginal based on these in based on this, you know, one prompt for running here, but you can already start to see some differences, right? This is a little bit of detail here, a little bit of extra kind of information. I like how it's breaking down the information a little bit more in O1 Pro mode. Let's run some more prompts and then we can make a better decision. Let's go ahead and run script to blog. So this is pretty interesting. Let's close this, let's close this, and let's focus in on our script to blog. What I'll do here is I'll open up a script from our previous YouTube video. So this is from the QWQ local reasoning model video that we put out last week. And what I'll do is I'll paste that script in here. So you can see this entire script right here. And then I'll paste in the video title. So we have our title in here as well. So we have a four K token prompt, not too large, but there's not nearly as much information to work through. So that's our hacker. new perspectives prompt. Let's copy this, hop back over to chat GPT Pro. Let's use new fresh windows so we don't contaminate and then let's just paste these prompts in and let's see how O1 and O1 Pro mode generates a HTML CSS only blog post based on the transcript from the last video we did on the channel. So right away you can see the same deal happening. We do get a detail of the inner monologue. Okay, O1 is finished already. This model is insanely fast. This is one of the most advanced. Okay, so it's it's in progress This model is just absolutely incredible. The speed here Is really making me appreciate the intelligence. It has even more. Oh, one preview would have taken probably about double the time to I'll put this but okay, so copy code. Let's go ahead and create a blog file here. So this is oh one QWQ blog post. HTML will paste this in and something important to note about this prompt here is that I also have image references. So you can see here inside the prompt, the meta prompt picked up on this perfectly. It didn't change the file references at all. We have a couple of PNGs, which will link directly to this images directory, right, with some images. So, you know, we do have images for this blog post. Let's see how O1 has done here in generating this. Let's go ahead and open up preview. Okay. Sorry if my screen blinded you there. Interesting. So we have the title, we have the hook that's something that we explicitly mentioned in the instructions. Start with a strong hook that captures the reader's attention and we have a table of contents here. It's this perfect. We did as for that search for table of contents, highlighting the three main points. Good. And then it kind of jumps into it, right? So this is really, really cool. This is a automatic generator blog post. I'm actually read this hook and see if I like this. So ever wonder if local reasing models can deliver the same nuance on the fly insights we've come to expect. from big cloud-based systems, good news. I just got a whole lot more interesting, neat QWQ, Quinn with questions. The model that's raising eyebrows in the world of local air reasoning, this is really good. In this post, we'll explore a trick that makes QWQ instantly more useful, prop chaining. Okay, so this is solid. You know, again, comment down below, let me know what you think about this hook, automatically generated by 01. It's pulling from the ideas of my intro from the previous YouTube video. And all, of course, link this video in the description, check it out, and then this blog post will make quite a bit more sense. But this is right on topic. Anyone that's seen our last video, this is exactly what it's all about. The whole idea in that video is we want to make QWQ more useful with prop chanings. So this is excellent. Table of contents, just like we asked, rise up the QWQ. Reasoning models, nicknamed it QWB in the intro. Here's the thumbnail for that video. Breaking down prop channing, it's talking about the reasoner and the extractor. Like we talked. about in the video, you can use QWQ to generate the long thinking output with the chain of thought. And then you can use a Quinn 2.5 or now Lama 3.3 to extract the result, right? And simplify the output. You can also now use Olamas structured outputs. So this just got released. I just wanted to quickly bring this up. Olama now has support for structured outputs, more reliability and consistency in JSON mode. So just something to bring up quickly here. You know, you can now use a Model like QWQ that thinks and you can extract. to the final result, right, with an extractor model that pulls out the example. So anyway, refocusing here on the O1 blog post output, we can see we have that prompt chain image placed really, really well. And you know, I actually meant to paste in the images inside of ChatGPT here. I completely just missed out on that. The O1 models now have image support, but it looks like very clearly O1 did not need to see these images to place them properly. So last bit of our blog here, I do like... these cards, right? Little bit of box shadow, image right in the center here, and you know, simple kind of lightweight section. So this is O1. Let's go ahead and look at the output from O1 in Pro mode. So I'm going to just copy the output here, open up a new file. This is going to be O1 Pro mode blogpost.html. We can paste this and let's go into HTML preview mode. So a crack open QWQ. Ready for it. So this is already better, right? It's not Not prefixing with hook, but let's open up O1's output. So it had this hook prefix, right? There's no reason to show that. So already, O1 ProBode understands the problem a little bit better than O1. So can we queue also known as Quorum questions? It's stirring excitement because it's not just another AM model. It's a reasoning powerhouse. You can run right on your machine. Let's be honest, can we queue to our output? Can be a bit messy. Don't worry. There's a brilliant solution, prompt chaining. And this post will walk through how prompt chaining transforms. to a super useful on-demand idea machine. Let's dive in. Okay, so pretty interesting, a little more exciting than I would like the language to be, but it's fine. We can tweak that very easily, but obtain the prompt. We have the table of contents, that's good. Problem with local reading models, I like this. So setting up a kind of intro, solution, example, structure. We can see these images here. We get this nice center of caption, which I think is a nice addition here. Introduction to prompt chaining, a game changer, prompt chaining, clever technique. It's really nailing exactly everything in that YouTube video. It's nailing every one of the ideas. You can use to generate rich thought out answers, then use another model like a Streamline Q2.5 model to neatly extract results. It's fantastic. How prompt training works in a nutshell. Great breakdown here. I do like that it kind of created this little how-to in the center here. Putting it all together, you can see we have our O1 image there and proper reasing models that through our prompt engine. at the bottom here, this is really cool. Pro tips, start simple, this is pretty nice, right? Gives a little pro tip item here. Why wait for tomorrow's models or next week's new code releases with prompt training, you can harness QtubeQ's reasoning capabilities today and keep a tidy, usable final result every time. So this is fantastic. To me, the language of O1 Pro mode is a lot more human. Maybe minus this intro, the intro's kind of annoying to me, but you know, that's fine. You might like intros like this. wrong with it. I will say I did like the format, like the the visual format of the base on model a little bit better, but I think that content wise, O1 Pro mode is a bit better here, right? These how-tos and this tip here and the you know image captions, I think provides quite a bit more value than the O1 base output blog. But again, you know, comment down below. Let me know what you think. Do you see one model being the other out or is it just close enough? I feel like it is kind of hard to tell. And these results could just be about the non-deterministic nature of LLMs as a whole, right? To me, a consistent theme here, a one-promoed is giving me just a little edge. Is it worth $200 a month? Well, that really just depends on the volume of reasoning that I need solved over the course of a month, right? I think that's the big kind of delineation with the decision of whether Chattagetipro is actually worth it. So we have one more prompt to run. Let's go ahead and fire that off. Open up our bug analysis. And so this is a really interesting one. Our bug analysis here, ignore the syntax here. That just looks like we clipped image a little bit there. All we need to do here is paste in some code. So I have this previous code base, Benchie, from our live benchmarking repository. And what I'll do here is I have this kind of pre-command loaded here. I'll be sharing more on this tool in 2025. But what I can do here is basically pull together context files. So if I run... I'll allow one gather files. Yeah, we can just see this command here. Basically all I'm doing here is I'm pulling all the view files in the source directory, TypeScript files and every Python file in the server. Okay, so this is a simple client server application. I'll also link the live benchmarking videos in the description if you're interested in checking out this code base and some of the cool live benchmarks we've done on the channel. So I'll copy this and this is gonna be really cool. So I'll close this and now if I print this, if I paste this, you can see. If I've run source or if I just search for source here, you can see every file from this code base, right? So this is really cool. So we have every Python file. We have every TypeScript and every VJS file that we need and This is great. So basically we have a bunch of code. You can see we have 20k token code base. So I'm just gonna take this Paste it in our user prompt. Now our expert code analysis prompt is going to run it analysis on that code. So as usual, we'll copy this. We'll start fresh sessions. And then I'll just paste in both sides and let's see how a one and a one pro mode reviews our code for us. So I'll paste this in both sides, you know, a decent size prompt there. This is a 20 K token prompt. And let's go ahead and scroll to the bottom here. Let's see what oh, one pro mode is doing. You can see it working through nice. We have a decent chain of details here. I have noticed that after while O1 Probo sometimes just stops outputting its details to you and you know just kind of keeps it to itself after a while. But this is nice to see, examining code, managing async bugs, it found a couple async bugs there after 32 seconds. So this is a more complex thing to work through. O1 thinking for about 32 seconds. And it found a decent chunk of issues for us, right? So let's go ahead and just copy this out. Everything's always easier to read in your own text editor. So we can see here for O1 base, a decent slew of responses. from our bug analysis prompt. We have a potential crash if meshes.content is empty from our Anthropic L and model. Yep, that's super solid. Everyone just kind of assumes that this will always exist. That was my bad to not, you know, place an F there. So we have our is loading resetting prematurely due to an unawaited a sync loop. Okay, so this is actually kind of interesting. Let me, let me actually just pull this off. Thankfully, we have this nice control F that we can use. And let's go in quick look for that.